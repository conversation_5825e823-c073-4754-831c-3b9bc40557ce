import { useState, useEffect, useCallback } from 'react';
import { useRewards } from '../context/RewardsProvider';
import { RewardRoll } from '../types';

/**
 * Hook to fetch a reward roll result for a specific round ID using the existing rewards system
 * @param roundId - The ID of the round to fetch the reward for
 * @param enabled - Whether the query should be enabled (default: true when roundId is provided)
 * @returns Query result containing the reward roll data
 */
export function useRollResult(roundId: string | null, enabled: boolean = true) {
  const { getPickResultByRoundId, loadingState } = useRewards();
  const [rewardRoll, setRewardRoll] = useState<RewardRoll | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const fetchReward = useCallback(async () => {
    if (!roundId || !enabled) {
      setRewardRoll(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await getPickResultByRoundId(roundId);
      setRewardRoll(result);
      console.log("Reward roll result:", roundId, result)
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch reward'));
      setRewardRoll(null);
    } finally {
      setIsLoading(false);
    }
  }, [roundId, enabled, getPickResultByRoundId]);

  useEffect(() => {
    fetchReward();
  }, [fetchReward]);

  return {
    rewardRoll,
    isLoading: isLoading || (loadingState.isLoading && loadingState.operation === 'fetching'),
    error,
    refetch: fetchReward,
    // Convenience properties
    hasReward: !!rewardRoll?.result?.reward,
    hasWon: !!rewardRoll?.result?.hasWon,
    isCompleted: rewardRoll?.status === 'completed',
    isPending: rewardRoll?.status === 'pending',
    isRolling: rewardRoll?.status === 'rolling',
    isFailed: rewardRoll?.status === 'failed',
  };
}