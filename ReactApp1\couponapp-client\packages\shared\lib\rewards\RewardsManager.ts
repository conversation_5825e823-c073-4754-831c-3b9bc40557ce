import { RewardMechanics, <PERSON>wardRoll, <PERSON>wardDefinition, <PERSON><PERSON><PERSON><PERSON>ory, <PERSON>wardPool, GameEventData } from './types';

const rewardRolls: RewardRoll[] = []
const rewardHistory: RewardHistory = {
     rewardPoolId: `pool-1`,
     rolls: [],
};


export const mockRewardPool : RewardPool = {
      id: `pool-1`,
      name: `Reward Pool for 1`,
      rewards: [
        {
          id: `reward-1`,
          name: "50% Off Coupon",
          description: "Get 50% off your next purchase",
          type: "coupon-code",
          image: {
            absoluteUrl: 'https://placehold.co/100x100/00ff00/ffffff?text=50OFF',
          },
        },
        {
          id: `reward-2`,
          name: "Free Shipping",
          description: "Free shipping on your next order",
          type: "claimable-url",
          image: {
            absoluteUrl: 'https://placehold.co/100x100/0000ff/ffffff?text=SHIP',
          },
        },
        {
          id: `reward-3`,
          name: "100% Off - Epic!",
          description: "Complete discount on selected items",
          type: "coupon-code",
          image: {
            absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=100OFF',
          },
        },
        {
          id: `reward-4`,
          name: "A CAR!",
          description: "Complete discount on selected items",
          type: "coupon-code",
          image: {
            absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=100OFF',
          },
        },
      ]
  }

export class RewardsManager {
  private gameWidgetId: string;
  private rewardMechanics: RewardMechanics;

  constructor(gameWidgetId: string, rewardMechanics: RewardMechanics) {
    this.gameWidgetId = gameWidgetId;
    this.rewardMechanics = rewardMechanics;

    //this is just to mock!
    rewardHistory.rewardPoolId = `pool-${gameWidgetId}`;
  }

  // Unified Event Handler
  public async handleGameEvent(
    triggerType: 'round_start' | 'round_finish' | 'game_finish',
    data: GameEventData
  ): Promise<RewardRoll | null> {
    const { roundId } = data;

    if (this.rewardMechanics.triggerOn === triggerType) {
      const rewardRoll = await this.rollReward(roundId);
      return rewardRoll;
    }

    return null;
  }



  // Reward Rolling
  private async rollReward(roundId: string): Promise<RewardRoll> {
    
    console.log("Rolling reard ", roundId)
    const rewardRoll: RewardRoll = {
      id: roundId + "_roll",
      roundId: roundId,
      gameWidgetId: this.gameWidgetId,
      timestamp: Date.now(),
      status: 'rolling',
    };
    console.log("Rolling reard rewardRoll:", rewardRoll)


    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      const hasWon = true

      let reward: RewardDefinition | undefined;
      if (hasWon) {
        reward = mockRewardPool.rewards[0];
      }

      rewardRoll.result = {
        hasWon,
        reward,
      };
      rewardRoll.status = 'completed';

    } catch (error) {
      rewardRoll.status = 'failed';
    }

    rewardRolls.push(rewardRoll);
    rewardHistory.rolls.push(rewardRoll);
    return rewardRoll;
  }


  // State Access - will be replaced with backend API calls later
  public async getPickResultByRoundId(roundId: string): Promise<RewardRoll> {
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log("Rolls: ", rewardRolls)
    
    for (const roll of Object.values(rewardRolls)) {
      if (roll.roundId === roundId) {
        return roll;
      }
    }

    return null;
  }

  public async getRewardHistory(): Promise<RewardHistory> {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return rewardHistory;
  }
}

